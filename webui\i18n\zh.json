{"Language": "简体中文", "Translation": {"Login Required": "需要登录", "Please login to access settings": "请登录后访问配置设置 (:gray[默认用户名: admin, 密码: admin, 您可以在 config.toml 中修改])", "Username": "用户名", "Password": "密码", "Login": "登录", "Login Error": "登录错误", "Incorrect username or password": "用户名或密码不正确", "Please enter your username and password": "请输入用户名和密码", "Video Script Settings": "**文案设置**", "Video Subject": "视频主题（给定一个关键词，:red[AI自动生成]视频文案）", "Script Language": "生成视频脚本的语言（一般情况AI会自动根据你输入的主题语言输出）", "Generate Video Script and Keywords": "点击使用AI根据**主题**生成 【视频文案】 和 【视频关键词】", "Auto Detect": "自动检测", "Video Script": "视频文案（:blue[①可不填，使用AI生成  ②合理使用标点断句，有助于生成字幕]）", "Generate Video Keywords": "点击使用AI根据**文案**生成【视频关键词】", "Please Enter the Video Subject": "请先填写视频文案", "Generating Video Script and Keywords": "AI正在生成视频文案和关键词...", "Generating Video Keywords": "AI正在生成视频关键词...", "Video Keywords": "视频关键词（:blue[①可不填，使用AI生成 ②用**英文逗号**分隔，只支持英文]）", "Video Settings": "**视频设置**", "Video Concat Mode": "视频拼接模式", "Random": "随机拼接（推荐）", "Sequential": "顺序拼接", "Video Transition Mode": "视频转场模式", "None": "无转场", "Shuffle": "随机转场", "FadeIn": "渐入", "FadeOut": "渐出", "SlideIn": "滑动入", "SlideOut": "滑动出", "Video Ratio": "视频比例", "Portrait": "竖屏 9:16（抖音视频）", "Landscape": "横屏 16:9（西瓜视频）", "Clip Duration": "视频片段最大时长(秒)（**不是视频总长度**，是指每个**合成片段**的长度）", "Number of Videos Generated Simultaneously": "同时生成视频数量", "Audio Settings": "**音频设置**", "Speech Synthesis": "朗读声音（:red[**与文案语言保持一致**。注意：V2版效果更好，但是需要API KEY]）", "Speech Region": "服务区域 (:red[必填，[点击获取](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key (:red[必填，密钥1 或 密钥2 均可 [点击获取](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "朗读音量（1.0表示100%）", "Speech Rate": "朗读速度（1.0表示1倍速）", "Male": "男性", "Female": "女性", "Background Music": "背景音乐", "No Background Music": "无背景音乐", "Random Background Music": "随机背景音乐", "Custom Background Music": "自定义背景音乐", "Custom Background Music File": "请输入自定义背景音乐的文件路径", "Background Music Volume": "背景音乐音量（0.2表示20%，背景声音不宜过高）", "Subtitle Settings": "**字幕设置**", "Enable Subtitles": "启用字幕（若取消勾选，下面的设置都将不生效）", "Font": "字幕字体", "Position": "字幕位置", "Top": "顶部", "Center": "中间", "Bottom": "底部（推荐）", "Custom": "自定义位置（70，表示离顶部70%的位置）", "Font Size": "字幕大小", "Font Color": "字幕颜色", "Stroke Color": "描边颜色", "Stroke Width": "描边粗细", "Generate Video": "生成视频", "Video Script and Subject Cannot Both Be Empty": "视频主题 和 视频文案，不能同时为空", "Generating Video": "正在生成视频，请稍候...", "Start Generating Video": "开始生成视频", "Video Generation Completed": "视频生成完成", "Video Generation Failed": "视频生成失败", "You can download the generated video from the following links": "你可以从以下链接下载生成的视频", "Basic Settings": "**基础设置** (:blue[点击展开])", "Language": "界面语言", "Pexels API Key": "Pexels API Key ([点击获取](https://www.pexels.com/api/)) :red[推荐使用]", "Pixabay API Key": "Pixabay API Key ([点击获取](https://pixabay.com/api/docs/#api_search_videos)) :red[可以不用配置，如果 Pexels 无法使用，再选择Pixabay]", "LLM Provider": "大模型提供商", "API Key": "API Key (:red[必填，需要到大模型提供商的后台申请])", "Base Url": "Base Url (可选)", "Account ID": "账户ID (Cloudflare的dash面板url中获取)", "Model Name": "模型名称 (:blue[需要到大模型提供商的后台确认被授权的模型名称])", "Please Enter the LLM API Key": "请先填写大模型 **API Key**", "Please Enter the Pexels API Key": "请先填写 **Pexels API Key**", "Please Enter the Pixabay API Key": "请先填写 **Pixabay API Key**", "Get Help": "有任何问题或建议，可以加入 **微信群** 求助或讨论：https://harryai.cc", "Video Source": "视频来源", "TikTok": "抖音 (TikTok 支持中，敬请期待)", "Bilibili": "哔哩哔哩 (Bilibili 支持中，敬请期待)", "Xiaohongshu": "小红书 (Xiaohongshu 支持中，敬请期待)", "Local file": "本地文件", "Play Voice": "试听语音合成", "Voice Example": "这是一段测试语音合成的示例文本", "Synthesizing Voice": "语音合成中，请稍候...", "TTS Provider": "语音合成提供商", "TTS Servers": "TTS服务器", "No voices available for the selected TTS server. Please select another server.": "当前选择的TTS服务器没有可用的声音，请选择其他服务器。", "SiliconFlow API Key": "硅基流动API密钥 [点击获取](https://cloud.siliconflow.cn/account/ak)", "SiliconFlow TTS Settings": "硅基流动TTS设置", "Speed: Range [0.25, 4.0], default is 1.0": "语速范围 [0.25, 4.0]，默认值为1.0", "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0": "音量：使用朗读音量设置，默认值1.0对应增益0", "Hide Log": "隐藏日志", "Hide Basic Settings": "隐藏基础设置\n\n隐藏后，基础设置面板将不会显示在页面中。\n\n如需要再次显示，请在 `config.toml` 中设置 `hide_config = false`", "LLM Settings": "**大模型设置**", "Video Source Settings": "**视频源设置**"}}